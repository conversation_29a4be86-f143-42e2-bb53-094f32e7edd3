import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/listening_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/listening_state.dart';

import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart'; // Added import

class ListeningMasteryResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  const ListeningMasteryResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<ListeningMasteryResultScreen> createState() =>
      _ListeningMasteryResultScreenState();
}

class _ListeningMasteryResultScreenState
    extends ConsumerState<ListeningMasteryResultScreen>
    with TickerProviderStateMixin {
  late AsyncValue<ListeningState> viewState;
  late ListeningController viewModel;
  late final AudioPlayer _bgmPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _playCongrats();
  }

  @override
  void dispose() {
    _bgmPlayer.dispose();
    super.dispose();
  }

  Future<void> _playCongrats() async {
    // Read the audio toggle state
    final isAudioEnabled = ref.read(audioToggleProvider);
    // Only play if audio is enabled
    if (isAudioEnabled) {
      await _bgmPlayer.play(AssetSource('sounds/score.mp3'));
    }
  }

  @override
  Widget build(BuildContext context) {
    final prov = ListeningControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
    );

    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);
    final totalCorrect = viewModel.calculateTotalCorrectPart();
    final totalWrong =
        viewState.value!.currentListenings.questions.length - totalCorrect;
    final score =
        (totalCorrect /
                viewState.value!.currentListenings.questions.length *
                100)
            .round();
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFFAFAFA),
                  Color(0xFFF5F5F5),
                  Color(0xFFFFFFFF),
                ],
                stops: [0.0, 0.3, 1.0],
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  const SizedBox(height: 40),
                  // Chapter title with modern styling
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    decoration: BoxDecoration(
                      color: const Color(0xff680007).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(
                        color: const Color(0xff680007).withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      '${context.loc.chapter} ${widget.chapter}',
                      style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        color: const Color(0xff680007),
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                  const SizedBox(height: 48),
                  // Score card with modern design
                  Container(
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                          spreadRadius: 0,
                        ),
                        BoxShadow(
                          color: Colors.black.withOpacity(0.04),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Animated score
                        TweenAnimationBuilder<int>(
                          duration: const Duration(milliseconds: 1800),
                          tween: IntTween(begin: 0, end: score),
                          curve: Curves.easeOutCubic,
                          builder: (context, animatedScore, child) {
                            return Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  colors: score > 85
                                      ? [const Color(0xff36AA34), const Color(0xff4CAF50)]
                                      : score > 60
                                      ? [const Color(0xffF5BE48), const Color(0xffFFC107)]
                                      : [const Color(0xff93000F), const Color(0xffE53935)],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: (score > 85
                                        ? const Color(0xff36AA34)
                                        : score > 60
                                        ? const Color(0xffF5BE48)
                                        : const Color(0xff93000F)).withOpacity(0.3),
                                    blurRadius: 20,
                                    offset: const Offset(0, 8),
                                  ),
                                ],
                              ),
                              child: Text(
                                '$animatedScore',
                                style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                                  fontSize: 64,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  height: 1,
                                ),
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 24),
                        // Score description
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF8F9FA),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            score > 85
                                ? context.loc.lm_100_desc
                                : score > 60
                                ? context.loc.lm_50_desc
                                : context.loc.lm_0_desc,
                            style: Theme.of(context).textTheme.titleMedium!.copyWith(
                              color: const Color(0xFF6B7280),
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  // Correct/Wrong statistics with modern cards
                  Row(
                    children: [
                      Expanded(
                        child: _correctWrongItem(
                          title: context.loc.correct_emot,
                          count: totalCorrect,
                          color: const Color(0xff36AA34),
                          isCorrect: true,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _correctWrongItem(
                          title: context.loc.wrong_emot,
                          count: totalWrong,
                          color: const Color(0xff93000F),
                          isCorrect: false,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 120), // Space for bottom buttons
                ],
              ),
            ),
          ),
          // const SizedBox(height: 66), // Removed as padding is handled inside SingleChildScrollView
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  VButtonGradient(
                    title: context.loc.next,
                    onTap: () => _handleNextButton(),
                    isBorder: false,
                  ),
                  SizedBox(height: 16),
                  VButtonGradient(
                    title: context.loc.share,
                    onTap: () => _handleNextButton(),
                    isBorder: false,
                    // backgroundColor: Colors.white,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      color: Colors.transparent,
                    ),
                    leading: Icon(Icons.share, color: Color(0xff998E8D)),
                    fontStyle: Theme.of(
                      context,
                    ).textTheme.bodyLarge!.copyWith(color: Color(0xff998E8D)),
                  ),
                ],
              ),
            ),
          ),
          if (viewState.value!.nextSection) _buildNextSectionDialog(),
        ],
      ),
    );
  }

  Widget _correctWrongItem({
    required String title,
    required int count,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: .4),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(width: 0.6, color: color),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium!.copyWith(
              color: const Color(0xffB4A9A7),
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          TweenAnimationBuilder<int>(
            duration: Duration(
              milliseconds: ((1800 * count / 5).round()).clamp(500, 1800),
            ),
            tween: IntTween(begin: 0, end: count),
            curve: Curves.easeOutCubic,
            builder: (context, animatedCount, child) {
              return Text(
                '$animatedCount',
                style: Theme.of(
                  context,
                ).textTheme.headlineLarge!.copyWith(color: color),
                textAlign: TextAlign.center,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNextSectionDialog() {
    return VDialogAlert(
      title: context.loc.nextSection,
      child: Column(
        children: [
          VButtonGradient(
            title: context.loc.yes,
            fontStyle: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white),
            onTap: () async {
              // First, reset the nextSection state to hide the dialog
              viewModel.resetNextSectionState();
              // Wait a frame to ensure the dialog is removed from the widget tree
              await Future.delayed(const Duration(milliseconds: 100));
              // Then proceed with navigation
              if (mounted) {
                viewModel.markSectionAsCompleted();
                // Navigate to speaking arena only when we've completed all listening content
                customNav(
                  context,
                  RouterName.speakingArenaOnboarding,
                  isReplace: true,
                  params: {'level': widget.level, 'chapter': widget.chapter},
                );
              }
            },
          ),
          const SizedBox(height: 24),
          VButtonGradient(
            title: context.loc.no,
            fontStyle: Theme.of(context).textTheme.bodyLarge,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14),
              color: Colors.transparent,
              border: Border.all(color: const Color(0xff802115), width: 0.6),
            ),
            onTap: () => context.pop(),
          ),
        ],
      ),
    );
  }

  Future<void> _handleNextButton() async {
    await viewModel.nextPart(context);
    if (viewState.value!.nextSection) {
      viewModel.markSectionAsCompleted();
    } else {}
  }
}
